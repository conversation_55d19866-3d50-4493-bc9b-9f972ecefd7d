# MessageReceiver Class Documentation

## Overview

The `MessageReceiver` class is a core component of the message handling system that extends `EventEmitter` to provide real-time streaming message reception capabilities. It processes Server-Sent Events (SSE) streams, parses message chunks, validates data integrity, and assembles complete message packages for consumption by the application.

### Key Features

- **Stream Processing**: Handles ReadableStream data from HTTP responses
- **Chunk Management**: Processes individual message chunks and assembles them into complete packages
- **Data Validation**: Ensures message integrity through chunk ordering and completeness validation
- **Event-Driven Architecture**: Emits events for different stages of message processing
- **Error Handling**: Comprehensive error detection and reporting
- **Real-time Updates**: Supports streaming display with partial content updates

## Architecture

The MessageReceiver operates on a two-level data structure:

1. **Chunks**: Basic units that carry message content with metadata (package_id, chunk_id, is_last, etc.)
2. **Packages**: Complete message units assembled from one or more chunks

### Data Flow

```
ReadableStream → Raw SSE Data → Parsed Chunks → Validated Packages → Events
```

## Constructor

### `constructor(response: ReadableStream)`

Creates a new MessageReceiver instance.

**Parameters:**
- `response: ReadableStream` - The readable stream containing SSE-formatted message data

**Initialization:**
- Sets up a ReadableStreamDefaultReader for stream processing
- Initializes a UTF-8 TextDecoder for data conversion
- Inherits EventEmitter capabilities for event handling

**Example:**
```typescript
const stream = response.body; // From fetch or axios response
const receiver = new MessageReceiver(stream);
```

## Public Methods

### `receive(): void`

Starts the message reception process. This method initiates continuous reading from the stream until completion or error.

**Behavior:**
- Prevents multiple concurrent reception processes
- Ignores calls if already receiving or completed
- Begins recursive stream reading

**Example:**
```typescript
receiver.receive();
```

### `stop(): void`

Stops the message reception process and releases resources.

**Behavior:**
- Cancels the stream reader if active
- Sets internal flags to prevent further processing
- Cleans up resources safely

**Example:**
```typescript
receiver.stop();
```

## Properties

### Public Properties

- `packages: IMessagePackage[]` - Array of successfully processed message packages
- `packageId: number` - ID of the currently processing package (-1 if none)
- `packageType: MessagePackageType | null` - Type of the current package
- `packageBuffer: MessageChunk[]` - Buffer storing chunks for the current package
- `chunkId: number` - ID of the current chunk being processed
- `reader: ReadableStreamDefaultReader | null` - Stream reader instance
- `decoder: TextDecoder | null` - UTF-8 text decoder

### Private Properties

- `isReceiving: boolean` - Flag indicating if reception is active
- `isDone: boolean` - Flag indicating if reception is complete

## Events

The MessageReceiver emits the following events through the EventEmitter interface:

### `MessageReceiverEvent.CHUNK_RECEIVED`
**Payload:** `MessageChunk`
**Triggered:** When a new chunk is received and parsed
**Usage:** Monitor individual chunk reception

### `MessageReceiverEvent.PACKAGE_RECEIVING`
**Payload:** `IMessagePackage` (with status: Loading)
**Triggered:** During package assembly for streaming display
**Usage:** Update UI with partial content for real-time display

### `MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED`
**Payload:** `IMessagePackage` (with status: Finished)
**Triggered:** When a complete package is assembled and validated
**Usage:** Process complete message packages

### `MessageReceiverEvent.HEADER_RECEIVED`
**Payload:** `IMessagePackage` (package_id: 0)
**Triggered:** When the first package (header) is received
**Usage:** Handle conversation metadata and initialization

### `MessageReceiverEvent.MESSAGE_FINISHED`
**Payload:** `MessageChunk` (with event_type: End)
**Triggered:** When an End event chunk is received
**Usage:** Handle message completion signals

### `MessageReceiverEvent.DONE`
**Payload:** None
**Triggered:** When the stream ends normally
**Usage:** Handle completion of message reception

### `MessageReceiverEvent.ERROR`
**Payload:** `MessageError`
**Triggered:** When any error occurs during processing
**Usage:** Handle various error conditions

### `MessageReceiverEvent.CANCELLED`
**Payload:** None
**Triggered:** When reception is cancelled
**Usage:** Handle cancellation scenarios

## Usage Examples

### Basic Usage

```typescript
import { MessageReceiver, MessageReceiverEvent } from '@/message/receiver';

// Create receiver from HTTP response
const response = await fetch('/api/chat/stream');
const receiver = new MessageReceiver(response.body);

// Listen for complete packages
receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, (pkg) => {
  console.log('Received package:', pkg);
  // Process complete package
});

// Listen for streaming updates
receiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, (pkg) => {
  console.log('Partial package:', pkg);
  // Update UI with partial content
});

// Handle errors
receiver.on(MessageReceiverEvent.ERROR, (error) => {
  console.error('Reception error:', error);
});

// Start reception
receiver.receive();
```

### Advanced Event Handling

```typescript
const receiver = new MessageReceiver(stream);

// Handle conversation initialization
receiver.on(MessageReceiverEvent.HEADER_RECEIVED, (headerPkg) => {
  const header = JSON.parse(headerPkg.data);
  console.log('Conversation ID:', header.conversation_id);
  console.log('Message ID:', header.message_id);
});

// Monitor individual chunks
receiver.on(MessageReceiverEvent.CHUNK_RECEIVED, (chunk) => {
  console.log(`Chunk ${chunk.chunk_id} of package ${chunk.package_id}`);
});

// Handle message completion
receiver.on(MessageReceiverEvent.MESSAGE_FINISHED, () => {
  console.log('Message stream completed');
});

// Handle stream completion
receiver.on(MessageReceiverEvent.DONE, () => {
  console.log('All packages received');
  console.log('Total packages:', receiver.packages.length);
});

receiver.receive();
```

### Integration with React Components

```typescript
import { useEffect, useState } from 'react';
import { MessageReceiver, MessageReceiverEvent } from '@/message/receiver';

function ChatComponent() {
  const [messages, setMessages] = useState([]);
  const [currentMessage, setCurrentMessage] = useState('');

  useEffect(() => {
    const receiver = new MessageReceiver(streamResponse);

    // Update current message during streaming
    receiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, (pkg) => {
      if (pkg.package_type === MessagePackageType.Text) {
        setCurrentMessage(pkg.data);
      }
    });

    // Finalize message when package is complete
    receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, (pkg) => {
      if (pkg.package_type === MessagePackageType.Text) {
        setMessages(prev => [...prev, pkg.data]);
        setCurrentMessage('');
      }
    });

    receiver.receive();

    return () => receiver.stop();
  }, [streamResponse]);

  return (
    <div>
      {messages.map((msg, i) => <div key={i}>{msg}</div>)}
      {currentMessage && <div className="streaming">{currentMessage}</div>}
    </div>
  );
}
```

## Integration Points

### With HTTP Clients

The MessageReceiver integrates with HTTP clients that support streaming responses:

```typescript
// With fetch API
const response = await fetch('/api/stream');
const receiver = new MessageReceiver(response.body);

// With axios (requires response stream)
const response = await axios.get('/api/stream', { responseType: 'stream' });
const receiver = new MessageReceiver(response.data);
```

### With Message System

The receiver works with other message system components:

- **Message Class**: Packages are used to create Message instances
- **PackageRender**: Renders received packages in the UI
- **AgentCore**: Orchestrates message flow in the application

### With State Management

```typescript
// Integration with state management
receiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, (pkg) => {
  store.dispatch(addMessagePackage(pkg));
});
```

## Error Handling

The MessageReceiver handles various error scenarios:

### Error Types

1. **Network Errors**: Connection issues, stream interruption
2. **Parsing Errors**: Invalid JSON in chunk data
3. **Validation Errors**: Incomplete packages, invalid chunk sequences
4. **Server Errors**: Error events from the server

### Error Handling Patterns

```typescript
receiver.on(MessageReceiverEvent.ERROR, (error: MessageError) => {
  switch (error.code) {
    case MessageErrorCode.NetworkError:
      // Handle connection issues
      console.error('Network error:', error.message);
      break;
    
    case MessageErrorCode.ParsingError:
      // Handle data parsing issues
      console.error('Parsing error:', error.message);
      break;
    
    case MessageErrorCode.IncompletePackage:
      // Handle incomplete package scenarios
      console.error('Incomplete package:', error.message);
      break;
    
    case MessageErrorCode.InvalidChunk:
      // Handle chunk validation errors
      console.error('Invalid chunk:', error.message);
      break;
    
    case MessageErrorCode.ServerError:
      // Handle server-side errors
      console.error('Server error:', error.message);
      break;
  }
});
```

## TypeScript Types

### Core Interfaces

```typescript
interface MessageChunk {
  package_id: number;
  package_type: MessagePackageType;
  chunk_id: number;
  is_last: boolean;
  data: string;
  event_id: number;
  event_type: EventType;
}

interface IMessagePackage {
  package_id: number;
  package_type: MessagePackageType;
  status: MessagePackageStatus;
  data: string;
}
```

### Enums

```typescript
enum MessagePackageType {
  Text = 0,
  Structured = 1,
  Thinking = 2,
  Error = 3
}

enum MessagePackageStatus {
  Loading = 0,
  Finished = 1,
  Error = 2
}

enum EventType {
  Start = 1000,
  Loading = 1001,
  End = 1002,
  Error = 2000
}

enum MessageReceiverEvent {
  HEADER_RECEIVED = "header_received",
  MESSAGE_FINISHED = "message_finished",
  PACKAGE_FINISHED_RECEIVED = "package_received",
  PACKAGE_RECEIVING = "package_receiving",
  CHUNK_RECEIVED = "chunk_received",
  DONE = "done",
  CANCELLED = "cancelled",
  ERROR = "error"
}
```

### Error Types

```typescript
enum MessageErrorCode {
  IncompletePackage = "INCOMPLETE_PACKAGE",
  InvalidChunk = "INVALID_CHUNK",
  ParsingError = "PARSING_ERROR",
  NetworkError = "NETWORK_ERROR",
  ServerError = "SERVER_ERROR"
}

class MessageError {
  message: string;
  code: MessageErrorCode;
  error: any;
}
```

## Best Practices

1. **Always handle errors**: Listen for ERROR events to handle various failure scenarios
2. **Clean up resources**: Call `stop()` when component unmounts or reception is no longer needed
3. **Use appropriate events**: Use PACKAGE_RECEIVING for streaming UI, PACKAGE_FINISHED_RECEIVED for final processing
4. **Validate package types**: Check package_type before processing package data
5. **Handle async operations**: Remember that all events are asynchronous
6. **Monitor memory usage**: Large streams may accumulate many packages; consider cleanup strategies

## Performance Considerations

- The receiver processes chunks sequentially and validates ordering
- Package assembly involves sorting and concatenation operations
- Event emission is synchronous within the event loop
- Memory usage grows with the number of packages retained
- Stream processing is optimized for real-time display scenarios

## Testing

The MessageReceiver includes comprehensive test coverage. See `__tests__/receiver.test.ts` for examples of:

- Basic message reception
- Multi-chunk package handling
- Error scenario testing
- Event emission verification
- Stream lifecycle management
